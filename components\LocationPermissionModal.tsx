"use client"

import { useState } from "react"
import { MapPin, AlertCircle, CheckCircle, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface LocationPermissionModalProps {
  isOpen: boolean
  onClose: () => void
  onPermissionGranted: () => void
  onPermissionDenied: () => void
  permission: PermissionState
  error?: string | null
}

export function LocationPermissionModal({
  isOpen,
  onClose,
  onPermissionGranted,
  onPermissionDenied,
  permission,
  error
}: LocationPermissionModalProps) {
  const [isRequesting, setIsRequesting] = useState(false)

  if (!isOpen) return null

  const handleRequestPermission = async () => {
    setIsRequesting(true)
    try {
      // 请求位置权限
      const result = await navigator.permissions.query({ name: 'geolocation' })

      if (result.state === 'granted') {
        onPermissionGranted()
      } else if (result.state === 'denied') {
        onPermissionDenied()
      } else {
        // 尝试获取位置来触发权限请求
        try {
          await navigator.geolocation.getCurrentPosition(
            () => onPermissionGranted(),
            () => onPermissionDenied(),
            { timeout: 10000 }
          )
        } catch {
          onPermissionDenied()
        }
      }
    } catch (error) {
      console.error('请求位置权限失败:', error)
      onPermissionDenied()
    } finally {
      setIsRequesting(false)
    }
  }

  const getPermissionIcon = () => {
    switch (permission) {
      case 'granted':
        return <CheckCircle className="w-8 h-8 text-green-500" />
      case 'denied':
        return <X className="w-8 h-8 text-red-500" />
      default:
        return <MapPin className="w-8 h-8 text-blue-500" />
    }
  }

  const getPermissionMessage = () => {
    switch (permission) {
      case 'granted':
        return {
          title: '位置权限已授权',
          description: '我们将使用您的位置为您推荐附近的药房',
          type: 'success' as const
        }
      case 'denied':
        return {
          title: '位置权限被拒绝',
          description: '您可以手动选择城市或在浏览器设置中重新启用位置权限',
          type: 'error' as const
        }
      default:
        return {
          title: '需要位置权限',
          description: '为了为您推荐附近的药房，我们需要获取您的位置信息',
          type: 'info' as const
        }
    }
  }

  const message = getPermissionMessage()

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md p-6 space-y-4">
        {/* 头部图标和标题 */}
        <div className="flex flex-col items-center text-center space-y-3">
          {getPermissionIcon()}
          <div>
            <h3 className="text-lg font-semibold">{message.title}</h3>
            <p className="text-sm text-gray-600 mt-1">{message.description}</p>
          </div>
        </div>

        {/* 权限说明 */}
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <MapPin className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium">位置信息用途</p>
              <p className="text-gray-600">用于计算您与药房的距离，推荐最近的药房</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium">隐私保护</p>
              <p className="text-gray-600">位置信息仅在本地使用，不会上传到服务器</p>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-3">
          {permission === 'prompt' && (
            <Button
              onClick={handleRequestPermission}
              disabled={isRequesting}
              className="flex-1"
            >
              {isRequesting ? '请求中...' : '允许获取位置'}
            </Button>
          )}

          {permission === 'granted' && (
            <Button
              onClick={onPermissionGranted}
              className="flex-1"
            >
              继续使用
            </Button>
          )}

          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            {permission === 'denied' ? '手动选择' : '暂不开启'}
          </Button>
        </div>

        {/* 底部说明 */}
        <p className="text-xs text-gray-500 text-center">
          您可以随时在浏览器设置中修改位置权限
        </p>
      </Card>
    </div>
  )
}
