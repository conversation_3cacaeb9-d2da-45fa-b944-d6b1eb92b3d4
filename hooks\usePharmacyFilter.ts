"use client"

import { useState, useEffect, useMemo, useCallback } from 'react';
import type { UserLocation } from '@/lib/geolocation';
import type {
  PharmacyWithLocation,
  SortOption,
  FilterOptions,
  SortedPharmacyResult
} from '@/lib/distance';
import {
  sortPharmacies,
  applyFilters,
  calculatePharmaciesDistances
} from '@/lib/distance';

// 药房过滤状态
export interface PharmacyFilterState {
  pharmacies: PharmacyWithLocation[];
  filteredPharmacies: SortedPharmacyResult[];
  userLocation: UserLocation | null;
  sortBy: SortOption;
  filters: FilterOptions;
  loading: boolean;
  error: string | null;
}

// Hook选项
export interface UsePharmacyFilterOptions {
  autoSort?: boolean;
  defaultSortBy?: SortOption;
  defaultFilters?: FilterOptions;
}

export function usePharmacyFilter(
  initialPharmacies: PharmacyWithLocation[],
  userLocation: UserLocation | null,
  options: UsePharmacyFilterOptions = {}
) {
  const {
    autoSort = true,
    defaultSortBy = 'distance',
    defaultFilters = {}
  } = options;

  const [state, setState] = useState<PharmacyFilterState>({
    pharmacies: initialPharmacies,
    filteredPharmacies: [],
    userLocation,
    sortBy: defaultSortBy,
    filters: defaultFilters,
    loading: false,
    error: null,
  });

  // 过滤和排序药房
  const processedPharmacies = useMemo(() => {
    if (!userLocation) {
      return [];
    }

    setState(prev => ({ ...prev, loading: true }));

    try {
      // 先应用过滤
      const filtered = applyFilters(initialPharmacies, userLocation, state.filters);

      // 再排序
      const sorted = sortPharmacies(filtered, userLocation, state.sortBy);

      setState(prev => ({ ...prev, loading: false, error: null }));

      return sorted;
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '处理药房数据失败'
      }));
      return [];
    }
  }, [initialPharmacies, userLocation, state.filters, state.sortBy]);

  // 更新过滤的药房列表
  useEffect(() => {
    setState(prev => ({
      ...prev,
      filteredPharmacies: processedPharmacies
    }));
  }, [processedPharmacies]);

  // 设置排序方式
  const setSortBy = useCallback((sortBy: SortOption) => {
    setState(prev => ({ ...prev, sortBy }));
  }, []);

  // 更新过滤条件
  const updateFilters = useCallback((newFilters: Partial<FilterOptions>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters }
    }));
  }, []);

  // 设置最大距离过滤
  const setMaxDistance = useCallback((maxDistance: number | undefined) => {
    updateFilters({ maxDistance });
  }, [updateFilters]);

  // 设置最小评分过滤
  const setMinRating = useCallback((minRating: number | undefined) => {
    updateFilters({ minRating });
  }, [updateFilters]);

  // 设置最大配送费过滤
  const setMaxDeliveryFee = useCallback((maxDeliveryFee: number | undefined) => {
    updateFilters({ maxDeliveryFee });
  }, [updateFilters]);

  // 切换仅显示营业中药房
  const setOpenOnly = useCallback((openOnly: boolean) => {
    updateFilters({ openOnly });
  }, [updateFilters]);

  // 设置标签过滤
  const setTags = useCallback((tags: string[] | undefined) => {
    updateFilters({ tags });
  }, [updateFilters]);

  // 重置所有过滤条件
  const resetFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {},
      sortBy: defaultSortBy
    }));
  }, [defaultSortBy]);

  // 清除错误
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // 获取统计信息
  const stats = useMemo(() => {
    if (!state.filteredPharmacies.length) {
      return {
        total: 0,
        averageDistance: 0,
        averageRating: 0,
        openCount: 0,
        closestPharmacy: null as SortedPharmacyResult | null,
        farthestPharmacy: null as SortedPharmacyResult | null,
      };
    }

    const distances = state.filteredPharmacies.map(p => p.distanceInfo.distance);
    const ratings = state.filteredPharmacies.map(p => p.pharmacy.rating);
    const openCount = state.filteredPharmacies.filter(p => p.pharmacy.isOpen).length;

    return {
      total: state.filteredPharmacies.length,
      averageDistance: Math.round((distances.reduce((a, b) => a + b, 0) / distances.length) * 10) / 10,
      averageRating: Math.round((ratings.reduce((a, b) => a + b, 0) / ratings.length) * 10) / 10,
      openCount,
      closestPharmacy: state.filteredPharmacies[0],
      farthestPharmacy: state.filteredPharmacies[state.filteredPharmacies.length - 1],
    };
  }, [state.filteredPharmacies]);

  return {
    // 状态
    pharmacies: state.filteredPharmacies,
    loading: state.loading,
    error: state.error,
    sortBy: state.sortBy,
    filters: state.filters,
    hasLocation: userLocation !== null,

    // 方法
    setSortBy,
    updateFilters,
    setMaxDistance,
    setMinRating,
    setMaxDeliveryFee,
    setOpenOnly,
    setTags,
    resetFilters,
    clearError,

    // 统计信息
    stats,

    // 辅助方法
    hasFilters: Object.keys(state.filters).some(key =>
      state.filters[key as keyof FilterOptions] !== undefined
    ),
    isFiltered: state.filteredPharmacies.length !== initialPharmacies.length,
  };
}
