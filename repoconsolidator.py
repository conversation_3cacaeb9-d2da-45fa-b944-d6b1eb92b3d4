#!/usr/bin/env python3
"""
Repo Consolidator - Frontend Route Pages Only
Extracts and consolidates frontend route pages from a Next.js project.
"""

import os
import sys
from pathlib import Path
from fnmatch import fnmatch

# Frontend route page patterns (Next.js app router)
FRONTEND_ROUTES = [
    'app/**/*.tsx',
    'app/**/*.jsx',
    'app/**/*.ts',
    'app/**/*.js',
    'pages/**/*.tsx',
    'pages/**/*.jsx',
    'pages/**/*.ts',
    'pages/**/*.js',
    'src/app/**/*.tsx',
    'src/app/**/*.jsx',
    'src/app/**/*.ts',
    'src/app/**/*.js',
    'src/pages/**/*.tsx',
    'src/pages/**/*.jsx',
    'src/pages/**/*.ts',
    'src/pages/**/*.js',
    'components/**/*.tsx',
    'components/**/*.jsx',
    'components/**/*.ts',
    'components/**/*.js',
    'src/components/**/*.tsx',
    'src/components/**/*.jsx',
    'src/components/**/*.ts',
    'src/components/**/*.js'
]

# Files to ignore
IGNORE_PATTERNS = [
    'node_modules/**',
    '.git/**',
    '.next/**',
    'out/**',
    'dist/**',
    'build/**',
    '*.log',
    '*.lock',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '*.min.js',
    '*.min.css',
    '*.map'
]

def should_ignore(file_path):
    """Check if file should be ignored"""
    for pattern in IGNORE_PATTERNS:
        if fnmatch(file_path, pattern):
            return True
    return False

def is_frontend_route(file_path):
    """Check if file is a frontend route page"""
    for pattern in FRONTEND_ROUTES:
        if fnmatch(file_path, pattern):
            return True
    return False

def extract_file_content(file_path):
    """Extract content from a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {e}"

def consolidate_frontend_routes(root_dir):
    """Consolidate frontend route pages"""
    print(f"🔍 Scanning for frontend route pages in: {root_dir}")
    print("=" * 60)

    frontend_files = []

    # Walk through directory
    for root, dirs, files in os.walk(root_dir):
        # Remove ignored directories
        dirs[:] = [d for d in dirs if not any(fnmatch(os.path.join(root, d), pattern) for pattern in IGNORE_PATTERNS)]

        for file in files:
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, root_dir)

            if should_ignore(relative_path):
                continue

            if is_frontend_route(relative_path):
                frontend_files.append(relative_path)

    print(f"📋 Found {len(frontend_files)} frontend route files:")
    for file in sorted(frontend_files):
        print(f"  • {file}")
    print()

    # Consolidate content
    consolidated = []
    consolidated.append("# Frontend Route Pages Consolidation")
    consolidated.append("=" * 50)
    consolidated.append("")

    for file_path in sorted(frontend_files):
        full_path = os.path.join(root_dir, file_path)
        content = extract_file_content(full_path)

        consolidated.append(f"## {file_path}")
        consolidated.append("-" * (len(file_path) + 4))
        consolidated.append("")
        consolidated.append("```typescript")
        consolidated.append(content)
        consolidated.append("```")
        consolidated.append("")
        consolidated.append("---")
        consolidated.append("")

    return "\n".join(consolidated)

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python repoconsolidator.py <directory>")
        sys.exit(1)

    root_dir = sys.argv[1]

    if not os.path.exists(root_dir):
        print(f"Error: Directory '{root_dir}' does not exist")
        sys.exit(1)

    # Generate consolidated content
    consolidated_content = consolidate_frontend_routes(root_dir)

    # Write to output file
    output_file = "frontend_routes_consolidated.md"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(consolidated_content)

    print(f"✅ Frontend route pages consolidated to: {output_file}")
    print(f"📊 Total files processed: {len([line for line in consolidated_content.split('\n') if line.startswith('## ')])}")

if __name__ == "__main__":
    main()
