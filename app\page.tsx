"use client"

import { useState, useMemo } from "react"
import { Search, ChevronLeft, MoreHorizontal, Target, Star, MapPin, Filter, Navigation } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"
import { useLocation } from "@/hooks/useLocation"
import { usePharmacyFilter } from "@/hooks/usePharmacyFilter"
import { LocationPermissionModal } from "@/components/LocationPermissionModal"
import { DistanceFilter } from "@/components/DistanceFilter"
import type { PharmacyWithLocation } from "@/lib/distance"

// 模拟药房数据
const MOCK_PHARMACIES: PharmacyWithLocation[] = [
  {
    id: "1",
    name: "复盛公大药房",
    address: "堡新街店",
    latitude: 37.8707,
    longitude: 112.5489,
    rating: 5.0,
    deliveryFee: 4,
    minOrder: 9.9,
    tags: ["品牌", "24小时"],
    isOpen: true,
  },
  {
    id: "2",
    name: "长城药店",
    address: "迎宾西街店",
    latitude: 37.8807,
    longitude: 112.5589,
    rating: 4.5,
    deliveryFee: 4,
    minOrder: 20,
    tags: ["品牌", "美团专送"],
    isOpen: true,
  },
  {
    id: "3",
    name: "一心堂药店",
    address: "晋中迎宾西街店",
    latitude: 37.8907,
    longitude: 112.5689,
    rating: 5.0,
    deliveryFee: 4,
    minOrder: 20,
    tags: ["品牌", "24小时", "美团专送"],
    isOpen: true,
  },
  {
    id: "4",
    name: "海川博泰医疗",
    address: "印象城店",
    latitude: 37.8607,
    longitude: 112.5389,
    rating: 4.9,
    deliveryFee: 4,
    minOrder: 14.9,
    tags: ["品牌"],
    isOpen: true,
  },
  {
    id: "5",
    name: "万民药房",
    address: "089泽盛苑店",
    latitude: 37.9007,
    longitude: 112.5789,
    rating: 4.0,
    deliveryFee: 5,
    minOrder: 20,
    tags: ["品牌", "美团专送"],
    isOpen: true,
  },
]

export default function PharmacyApp() {
  const [showLocationModal, setShowLocationModal] = useState(false)
  const [showDistanceFilter, setShowDistanceFilter] = useState(false)

  // 位置管理
  const {
    location,
    loading: locationLoading,
    error: locationError,
    permission,
    available: locationAvailable,
    getLocation,
    canRequestLocation,
  } = useLocation({
    autoRequest: false, // 手动触发位置请求
  })

  // 药房过滤
  const {
    pharmacies,
    loading: filterLoading,
    error: filterError,
    setMaxDistance,
    stats,
    hasFilters,
    isFiltered,
  } = usePharmacyFilter(MOCK_PHARMACIES, location)

  const handleLocationRequest = async () => {
    if (!canRequestLocation) {
      setShowLocationModal(true)
      return
    }

    await getLocation()
  }

  const handleLocationModalClose = () => {
    setShowLocationModal(false)
  }

  const handlePermissionGranted = () => {
    setShowLocationModal(false)
    getLocation()
  }

  const handlePermissionDenied = () => {
    setShowLocationModal(false)
  }
  return (
    <div className="min-h-screen bg-gray-50 max-w-sm mx-auto overflow-x-hidden">
      {/* Status Bar */}
      <div className="flex justify-between items-center px-3 py-2 bg-white text-sm">
        <div className="flex items-center gap-1">
          <span className="font-medium">21:38</span>
          <div className="flex gap-1">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">🐵</span>
              </div>
            ))}
            <span className="text-gray-400 text-xs">•••</span>
          </div>
        </div>
        <div className="flex items-center gap-1 text-gray-600 text-xs">
          <span>👁</span>
          <span>📱</span>
          <span>🔔</span>
          <span>📶</span>
          <span>5G</span>
          <span>📶</span>
          <span className="border rounded px-1">(36)</span>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-3 py-3 flex items-center gap-2">
        <ChevronLeft className="w-5 h-5 flex-shrink-0" />
        <div className="flex-1 flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="输入商家或商品名~"
              className="pl-10 pr-3 py-2 rounded-full border-2 border-yellow-300 bg-yellow-50 text-sm"
            />
          </div>
          <Button className="bg-yellow-400 hover:bg-yellow-500 text-black rounded-full px-4 py-2 text-sm flex-shrink-0">
            搜索
          </Button>
        </div>
        <MoreHorizontal className="w-5 h-5 flex-shrink-0" />
        <Target className="w-6 h-6 border-2 border-black rounded-full p-1 flex-shrink-0" />
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white px-3 py-3">
        <div className="flex gap-4 overflow-x-auto scrollbar-hide">
          <div className="text-center flex-shrink-0">
            <span className="text-black font-medium border-b-2 border-yellow-400 pb-1 whitespace-nowrap">附近商家</span>
          </div>
          <span className="text-gray-600 flex-shrink-0 whitespace-nowrap">心脏病用药</span>
          <span className="text-gray-600 flex-shrink-0 whitespace-nowrap">高血压用药</span>
          <span className="text-gray-600 flex-shrink-0 whitespace-nowrap">常见药</span>
        </div>
      </div>

      {/* Location Status */}
      <div className="bg-white px-3 py-3 border-t">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <MapPin className={`w-4 h-4 ${location ? 'text-green-500' : 'text-gray-400'}`} />
            <span className="text-sm text-gray-600">
              {location ? `当前位置: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}` : '未获取位置'}
            </span>
            {locationLoading && <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleLocationRequest}
            disabled={locationLoading}
            className="text-xs"
          >
            {location ? <Navigation className="w-3 h-3 mr-1" /> : <MapPin className="w-3 h-3 mr-1" />}
            {location ? '刷新位置' : '获取位置'}
          </Button>
        </div>

        {/* Location Error */}
        {(locationError || filterError) && (
          <Alert className="mb-3">
            <AlertDescription className="text-sm">
              {locationError || filterError}
            </AlertDescription>
          </Alert>
        )}

        {/* Filter Stats */}
        {location && stats.total > 0 && (
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <span>共找到 {stats.total} 家药房</span>
            {hasFilters && <span>已应用筛选条件</span>}
            {isFiltered && <span>显示 {pharmacies.length} 家</span>}
          </div>
        )}
      </div>

      {/* Filter Options */}
      <div className="bg-white px-3 py-3 border-t">
        <div className="flex justify-between items-center">
          <div className="flex gap-4 overflow-x-auto scrollbar-hide">
            <span className="text-gray-700 text-sm flex-shrink-0 whitespace-nowrap">全店满减</span>
            <span className="text-gray-700 text-sm flex-shrink-0 whitespace-nowrap">减配送费</span>
            <span className="text-gray-700 text-sm flex-shrink-0 whitespace-nowrap">领满减券</span>
            <span className="text-gray-700 text-sm flex-shrink-0 whitespace-nowrap">品牌商家</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDistanceFilter(true)}
            className="flex items-center gap-1 flex-shrink-0 ml-2"
          >
            <Filter className="w-4 h-4" />
            <span className="text-xs">筛选</span>
          </Button>
        </div>
      </div>

      {/* Store List */}
      <div className="px-3 py-2 space-y-3">
        {filterLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-2 text-sm text-gray-600">计算距离中...</span>
          </div>
        ) : !location ? (
          <div className="text-center py-8">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 text-sm mb-4">请先获取您的位置以查看附近药房</p>
            <Button onClick={handleLocationRequest} disabled={locationLoading}>
              {locationLoading ? '获取中...' : '获取位置'}
            </Button>
          </div>
        ) : pharmacies.length === 0 ? (
          <div className="text-center py-8">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 text-sm">附近暂无符合条件的药房</p>
          </div>
        ) : (
          pharmacies.map((pharmacyData) => {
            const { pharmacy, distanceInfo } = pharmacyData;
            const monthlySales = pharmacy.id === "1" ? "1000+" :
                               pharmacy.id === "2" ? "100+" :
                               pharmacy.id === "3" ? "300+" :
                               pharmacy.id === "4" ? "600+" : "100+";

            return (
              <Link key={pharmacy.id} href="/tail_page" className="block">
                <Card className="p-3 bg-white hover:bg-gray-50 transition-colors cursor-pointer active:scale-95">
                  <div className="flex gap-3">
                    <div className="relative flex-shrink-0">
                      <div className={`w-14 h-14 rounded-lg flex items-center justify-center ${
                        pharmacy.id === "1" ? "bg-yellow-400" :
                        pharmacy.id === "2" ? "bg-teal-500" :
                        pharmacy.id === "3" ? "bg-green-600" :
                        pharmacy.id === "4" ? "bg-blue-500" : "bg-orange-400"
                      }`}>
                        <span className={`text-xs font-bold text-center leading-tight ${
                          pharmacy.id === "1" ? "text-black" : "text-white"
                        }`}>
                          {pharmacy.name.includes("复盛") ? (
                            <>
                              复盛
                              <br />
                              公大
                              <br />
                              药房
                            </>
                          ) : pharmacy.name.includes("长城") ? (
                            <>
                              长城
                              <br />
                              药店
                            </>
                          ) : pharmacy.name.includes("一心堂") ? (
                            "一心堂"
                          ) : pharmacy.name.includes("海川") ? (
                            <>
                              海川
                              <br />
                              博泰
                            </>
                          ) : (
                            <>
                              万民
                              <br />
                              药房
                            </>
                          )}
                        </span>
                      </div>
                      {pharmacy.isOpen && (
                        <Badge className="absolute -bottom-1 left-0 bg-red-600 text-white text-xs px-1 rounded">
                          {pharmacy.tags.includes("24小时") ? "24小时营业" :
                           pharmacy.name.includes("长城") ? "即将打烊" : "营业中"}
                        </Badge>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {pharmacy.tags.includes("品牌") && (
                          <Badge className={`text-white text-xs px-2 flex-shrink-0 ${
                            pharmacy.id === "1" ? "bg-yellow-400" :
                            pharmacy.id === "2" ? "bg-teal-500" :
                            pharmacy.id === "3" ? "bg-green-600" :
                            pharmacy.id === "4" ? "bg-blue-500" : "bg-orange-400"
                          }`}>
                            品牌
                          </Badge>
                        )}
                        <h3 className="font-medium text-sm truncate">
                          {pharmacy.name}（{pharmacy.address}）
                        </h3>
                        {pharmacy.tags.includes("美团专送") && (
                          <Badge className="bg-orange-400 text-white text-xs px-2 flex-shrink-0 ml-auto">
                            美团专送
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mb-2 text-sm">
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <Star className="w-3 h-3 fill-orange-400 text-orange-400" />
                          <span className="font-medium">{pharmacy.rating}</span>
                        </div>
                        <span className="text-gray-500 flex-shrink-0">月售{monthlySales}</span>
                        <div className="flex items-center gap-1 ml-auto flex-shrink-0">
                          <MapPin className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-700 font-medium">{distanceInfo.formattedDistance}</span>
                          <span className="text-gray-500">•</span>
                          <span className="text-gray-500">{distanceInfo.duration}分钟</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mb-2 text-sm">
                        <span className="flex-shrink-0">起送 ¥{pharmacy.minOrder}</span>
                        <span className="flex-shrink-0">配送 ¥{pharmacy.deliveryFee}</span>
                        {pharmacy.id === "2" && (
                          <span className="text-sm line-through text-gray-400 flex-shrink-0">¥5</span>
                        )}
                        {pharmacy.id === "4" && (
                          <span className="text-sm line-through text-gray-400 flex-shrink-0">¥9</span>
                        )}
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        {pharmacy.id === "1" && (
                          <>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              25减1
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              新客减2
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              4.32折起
                            </Badge>
                          </>
                        )}
                        {pharmacy.id === "2" && (
                          <>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              新客减1
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              0.02元爆品
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              1.18折起
                            </Badge>
                          </>
                        )}
                        {pharmacy.id === "3" && (
                          <>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              新客减2
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              1.67折起
                            </Badge>
                          </>
                        )}
                        {pharmacy.id === "4" && (
                          <>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              19减1
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              39减2
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              69减3
                            </Badge>
                          </>
                        )}
                        {pharmacy.id === "5" && (
                          <>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              新客减2
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              0.98元爆品
                            </Badge>
                            <Badge variant="outline" className="text-orange-500 border-orange-500 text-xs">
                              3.92折起
                            </Badge>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            );
          })
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t">
        <div className="flex justify-around py-2">
          <div className="flex flex-col items-center gap-1 text-yellow-500">
            <div className="w-6 h-6 bg-yellow-400 rounded-full"></div>
            <span className="text-xs">首页</span>
          </div>
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <div className="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
              <span className="text-xs">👨</span>
            </div>
            <span className="text-xs">问医生</span>
          </div>
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <div className="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
              <span className="text-xs">😊</span>
            </div>
            <span className="text-xs">我的</span>
          </div>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-16"></div>

      {/* Location Permission Modal */}
      <LocationPermissionModal
        isOpen={showLocationModal}
        onClose={handleLocationModalClose}
        onPermissionGranted={handlePermissionGranted}
        onPermissionDenied={handlePermissionDenied}
        permission={permission}
        error={locationError}
      />

      {/* Distance Filter Modal */}
      <DistanceFilter
        isOpen={showDistanceFilter}
        onClose={() => setShowDistanceFilter(false)}
        onDistanceChange={(distance) => {
          setMaxDistance(distance)
          setShowDistanceFilter(false)
        }}
      />
    </div>
  )
}
