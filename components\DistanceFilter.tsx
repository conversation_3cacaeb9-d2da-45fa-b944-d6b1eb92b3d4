"use client"

import { useState } from "react"
import { MapP<PERSON>, Filter, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface DistanceFilterProps {
  isOpen: boolean
  onClose: () => void
  currentDistance?: number
  onDistanceChange: (distance: number | undefined) => void
}

const DISTANCE_OPTIONS = [
  { value: 1, label: '1公里' },
  { value: 3, label: '3公里' },
  { value: 5, label: '5公里' },
  { value: 10, label: '10公里' },
  { value: 20, label: '20公里' },
  { value: undefined, label: '不限距离' },
]

export function DistanceFilter({
  isOpen,
  onClose,
  currentDistance,
  onDistanceChange
}: DistanceFilterProps) {
  const [selectedDistance, setSelectedDistance] = useState<number | undefined>(currentDistance)

  if (!isOpen) return null

  const handleDistanceSelect = (distance: number | undefined) => {
    setSelectedDistance(distance)
  }

  const handleApply = () => {
    onDistanceChange(selectedDistance)
    onClose()
  }

  const handleClear = () => {
    setSelectedDistance(undefined)
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-end justify-center z-50">
      <Card className="w-full max-w-sm mx-4 mb-4 p-6 rounded-t-2xl">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-semibold">距离筛选</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 当前选择显示 */}
        {selectedDistance && (
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">当前选择</p>
            <Badge variant="secondary" className="text-blue-600 border-blue-200">
              {selectedDistance}公里以内
            </Badge>
          </div>
        )}

        {/* 距离选项 */}
        <div className="space-y-3 mb-6">
          <p className="text-sm font-medium text-gray-700">选择距离范围</p>
          <div className="grid grid-cols-2 gap-3">
            {DISTANCE_OPTIONS.map((option) => (
              <button
                key={option.label}
                onClick={() => handleDistanceSelect(option.value)}
                className={`
                  p-3 rounded-lg border-2 text-left transition-colors
                  ${selectedDistance === option.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }
                `}
              >
                <div className="flex items-center gap-2">
                  <MapPin className={`w-4 h-4 ${
                    selectedDistance === option.value ? 'text-blue-500' : 'text-gray-400'
                  }`} />
                  <span className="font-medium">{option.label}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 筛选说明 */}
        <div className="bg-blue-50 p-3 rounded-lg mb-6">
          <p className="text-sm text-blue-800">
            💡 设置距离范围后，只显示符合条件的药房。距离越近，配送越快！
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleClear}
            className="flex-1"
          >
            清除筛选
          </Button>
          <Button
            onClick={handleApply}
            className="flex-1"
          >
            应用筛选
          </Button>
        </div>
      </Card>
    </div>
  )
}
