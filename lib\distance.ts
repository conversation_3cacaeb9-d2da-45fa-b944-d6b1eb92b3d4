import { getDistance, getBoundsOfDistance, isPointInPolygon } from 'geolib';
import type { UserLocation, PharmacyLocation, DistanceResult } from './geolocation';

// 药房数据接口（带位置信息）
export interface PharmacyWithLocation extends PharmacyLocation {
  rating: number;
  deliveryFee: number;
  minOrder: number;
  tags: string[];
  isOpen: boolean;
}

// 排序选项
export type SortOption = 'distance' | 'rating' | 'delivery_time' | 'min_order';

// 过滤选项
export interface FilterOptions {
  maxDistance?: number;
  minRating?: number;
  maxDeliveryFee?: number;
  openOnly?: boolean;
  tags?: string[];
}

// 排序结果
export interface SortedPharmacyResult {
  pharmacy: PharmacyWithLocation;
  distanceInfo: DistanceResult;
}

/**
 * 计算多个药房到用户的距离
 */
export function calculatePharmaciesDistances(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation
): SortedPharmacyResult[] {
  return pharmacies.map(pharmacy => {
    const distance = getDistance(userLocation, pharmacy);
    const distanceKm = Math.round(distance / 1000 * 10) / 10;

    return {
      pharmacy,
      distanceInfo: {
        pharmacyId: pharmacy.id,
        distance: distanceKm,
        duration: estimateDeliveryTime(distanceKm),
        formattedDistance: formatDistance(distanceKm),
      }
    };
  });
}

/**
 * 按距离排序药房
 */
export function sortByDistance(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation
): SortedPharmacyResult[] {
  const withDistances = calculatePharmaciesDistances(pharmacies, userLocation);
  return withDistances.sort((a, b) => a.distanceInfo.distance - b.distanceInfo.distance);
}

/**
 * 按评分排序药房
 */
export function sortByRating(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation
): SortedPharmacyResult[] {
  const withDistances = calculatePharmaciesDistances(pharmacies, userLocation);
  return withDistances.sort((a, b) => b.pharmacy.rating - a.pharmacy.rating);
}

/**
 * 按配送时间排序药房
 */
export function sortByDeliveryTime(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation
): SortedPharmacyResult[] {
  const withDistances = calculatePharmaciesDistances(pharmacies, userLocation);
  return withDistances.sort((a, b) => a.distanceInfo.duration - b.distanceInfo.duration);
}

/**
 * 按起送价排序药房
 */
export function sortByMinOrder(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation
): SortedPharmacyResult[] {
  const withDistances = calculatePharmaciesDistances(pharmacies, userLocation);
  return withDistances.sort((a, b) => a.pharmacy.minOrder - b.pharmacy.minOrder);
}

/**
 * 通用排序函数
 */
export function sortPharmacies(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation,
  sortBy: SortOption = 'distance'
): SortedPharmacyResult[] {
  switch (sortBy) {
    case 'rating':
      return sortByRating(pharmacies, userLocation);
    case 'delivery_time':
      return sortByDeliveryTime(pharmacies, userLocation);
    case 'min_order':
      return sortByMinOrder(pharmacies, userLocation);
    case 'distance':
    default:
      return sortByDistance(pharmacies, userLocation);
  }
}

/**
 * 按距离过滤药房
 */
export function filterByDistance(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation,
  maxDistance: number
): PharmacyWithLocation[] {
  return pharmacies.filter(pharmacy => {
    const distance = getDistance(userLocation, pharmacy);
    const distanceKm = distance / 1000;
    return distanceKm <= maxDistance;
  });
}

/**
 * 按评分过滤药房
 */
export function filterByRating(
  pharmacies: PharmacyWithLocation[],
  minRating: number
): PharmacyWithLocation[] {
  return pharmacies.filter(pharmacy => pharmacy.rating >= minRating);
}

/**
 * 按配送费过滤药房
 */
export function filterByDeliveryFee(
  pharmacies: PharmacyWithLocation[],
  maxFee: number
): PharmacyWithLocation[] {
  return pharmacies.filter(pharmacy => pharmacy.deliveryFee <= maxFee);
}

/**
 * 过滤营业中的药房
 */
export function filterOpenOnly(
  pharmacies: PharmacyWithLocation[]
): PharmacyWithLocation[] {
  return pharmacies.filter(pharmacy => pharmacy.isOpen);
}

/**
 * 按标签过滤药房
 */
export function filterByTags(
  pharmacies: PharmacyWithLocation[],
  tags: string[]
): PharmacyWithLocation[] {
  return pharmacies.filter(pharmacy =>
    tags.some(tag => pharmacy.tags.includes(tag))
  );
}

/**
 * 应用多个过滤条件
 */
export function applyFilters(
  pharmacies: PharmacyWithLocation[],
  userLocation: UserLocation,
  filters: FilterOptions
): PharmacyWithLocation[] {
  let filtered = [...pharmacies];

  if (filters.maxDistance !== undefined) {
    filtered = filterByDistance(filtered, userLocation, filters.maxDistance);
  }

  if (filters.minRating !== undefined) {
    filtered = filterByRating(filtered, filters.minRating);
  }

  if (filters.maxDeliveryFee !== undefined) {
    filtered = filterByDeliveryFee(filtered, filters.maxDeliveryFee);
  }

  if (filters.openOnly) {
    filtered = filterOpenOnly(filtered);
  }

  if (filters.tags && filters.tags.length > 0) {
    filtered = filterByTags(filtered, filters.tags);
  }

  return filtered;
}

/**
 * 获取距离范围内的药房
 */
export function getPharmaciesInRadius(
  pharmacies: PharmacyWithLocation[],
  center: { latitude: number; longitude: number },
  radiusInKm: number
): PharmacyWithLocation[] {
  const bounds = getBoundsOfDistance(center, radiusInKm * 1000);

  return pharmacies.filter(pharmacy => {
    const point = { latitude: pharmacy.latitude, longitude: pharmacy.longitude };
    return isPointInPolygon(point, [
      { latitude: bounds[0].latitude, longitude: bounds[0].longitude },
      { latitude: bounds[0].latitude, longitude: bounds[1].longitude },
      { latitude: bounds[1].latitude, longitude: bounds[1].longitude },
      { latitude: bounds[1].latitude, longitude: bounds[0].longitude },
    ]);
  });
}

/**
 * 预估配送时间（分钟）
 */
export function estimateDeliveryTime(distance: number): number {
  const baseTime = 10; // 基础准备时间
  const timePerKm = 3; // 每公里3分钟
  return Math.round(baseTime + distance * timePerKm);
}

/**
 * 格式化距离显示
 */
export function formatDistance(distance: number): string {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  } else if (distance < 10) {
    return `${distance.toFixed(1)}km`;
  } else {
    return `${Math.round(distance)}km`;
  }
}

/**
 * 格式化配送时间显示
 */
export function formatDeliveryTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}分钟`;
  } else {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}小时${mins}分钟`;
  }
}
