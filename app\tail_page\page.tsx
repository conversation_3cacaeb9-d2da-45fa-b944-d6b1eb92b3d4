"use client"

import { ArrowLeft, Search, Plus, MessageCircle, ShoppingCart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useState } from "react"

export default function StorePage() {
  const [activeCategory, setActiveCategory] = useState("recommend")

  const categories = [
    { id: "recommend", name: "推荐", icon: "🔥", color: "bg-orange-100 text-orange-600" },
    { id: "newuser", name: "新人", icon: "🎯", color: "bg-red-100 text-red-600" },
    { id: "member", name: "会员", icon: "💎", color: "bg-red-100 text-red-600" },
    { id: "cold", name: "感冒用药", color: "bg-gray-50 text-gray-700" },
    { id: "respiratory", name: "呼吸系统", color: "bg-gray-50 text-gray-700" },
    { id: "antibacterial", name: "抗菌消炎", color: "bg-gray-50 text-gray-700" },
    { id: "durex", name: "杜蕾斯专区", color: "bg-gray-50 text-gray-700" },
    { id: "supplement", name: "杰士邦真持久", color: "bg-gray-50 text-gray-700" },
    { id: "pregnancy", name: "验孕避孕", color: "bg-gray-50 text-gray-700" },
    { id: "fever", name: "清热解暑", color: "bg-gray-50 text-gray-700" },
    { id: "digestive", name: "曼秀雷敦", color: "bg-gray-50 text-gray-700" },
    { id: "skincare", name: "凄单专区", color: "bg-gray-50 text-gray-700" },
  ]

  const products = [
    {
      id: 1,
      name: "[利尔康]碘伏消毒液100ml/瓶",
      image:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ce8aafd6b7c13d39e9eb50dbbfc6b52b.jpg-Seu3KvRh2miRcGwXjjIaXyxcd99Bzx.jpeg",
      originalPrice: 3,
      currentPrice: 0,
      discount: "药品新客0.33折",
      badge: "新客直降￥3",
    },
    {
      id: 2,
      name: "[太极]藿香正气口服液10ml*10支/盒",
      image:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ce8aafd6b7c13d39e9eb50dbbfc6b52b.jpg-Seu3KvRh2miRcGwXjjIaXyxcd99Bzx.jpeg",
      originalPrice: 14.06,
      currentPrice: 7.9,
      discount: "药品新客4.05折",
      badge: "新客直降￥14.06",
    },
    {
      id: 3,
      name: "[999]感冒灵颗粒10g*9袋/盒",
      image:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ce8aafd6b7c13d39e9eb50dbbfc6b52b.jpg-Seu3KvRh2miRcGwXjjIaXyxcd99Bzx.jpeg",
      originalPrice: 2.41,
      currentPrice: 14.8,
      discount: "9.18折 限2份",
      badge: "已优惠￥2.41",
    },
    {
      id: 4,
      name: "[芬必得]布洛芬缓释胶囊0.3g*24粒/盒",
      image:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ce8aafd6b7c13d39e9eb50dbbfc6b52b.jpg-Seu3KvRh2miRcGwXjjIaXyxcd99Bzx.jpeg",
      originalPrice: 5.27,
      currentPrice: 21.53,
      discount: "8.41折 限2份",
      badge: "仅剩1份",
    },
    {
      id: 5,
      name: "[可可康]藿香正气水10ml*8支/盒",
      image:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ce8aafd6b7c13d39e9eb50dbbfc6b52b.jpg-Seu3KvRh2miRcGwXjjIaXyxcd99Bzx.jpeg",
      originalPrice: 5.3,
      currentPrice: 3.5,
      discount: "5.11折 限2份",
      badge: "已优惠￥5.3",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 max-w-sm mx-auto overflow-x-hidden">
      {/* Status Bar */}
      <div className="bg-white text-gray-600 px-3 py-2 flex justify-between items-center text-sm border-b">
        <div className="flex items-center gap-1">
          <span className="font-medium">21:38</span>
          <div className="flex gap-1">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">🐵</span>
              </div>
            ))}
            <span className="text-gray-400 text-xs">•••</span>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <span>👁</span>
          <span>📱</span>
          <span>🔔</span>
          <span>📶</span>
          <span>5G</span>
          <span>📶</span>
          <span className="border rounded px-1">(36)</span>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-3 py-3 border-b">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
            <h1 className="text-lg font-medium text-gray-800">长城药店</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-gray-600 hover:bg-gray-100">
              <span>•••</span>
            </Button>
            <Button variant="outline" size="sm" className="text-gray-600 border-gray-300 hover:bg-gray-50">
              收藏
            </Button>
          </div>
        </div>

        {/* Store Info */}
        <div className="flex items-center gap-3 mb-2">
          <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">长城药店</span>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Badge className="bg-yellow-600 text-white text-xs">
                配送约35分钟
              </Badge>
              <span className="text-sm text-gray-500">"包装好"</span>
              <Badge className="bg-blue-600 text-white text-xs">
                美团专送
              </Badge>
            </div>
            <p className="text-sm text-gray-500">公告：欢迎光临，很高兴为您服务</p>
          </div>
        </div>

        {/* Promotion Tags */}
        <div className="flex gap-2 overflow-x-auto scrollbar-hide">
          <Badge className="bg-red-600 text-white text-xs whitespace-nowrap">减1元配送费</Badge>
          <Badge className="bg-red-600 text-white text-xs whitespace-nowrap">新客减1</Badge>
          <Badge className="bg-red-600 text-white text-xs whitespace-nowrap">0.02元爆品</Badge>
          <Badge className="bg-red-600 text-white text-xs whitespace-nowrap">1.18折起</Badge>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white px-3 py-3 border-b sticky top-0 z-10">
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索店内商品"
              className="w-full pl-10 pr-3 py-2 bg-gray-100 rounded-full text-sm"
            />
          </div>
          <Button className="bg-amber-700 hover:bg-amber-800 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">
            ✓ 商家会员
          </Button>
        </div>
      </div>

      <div className="flex bg-white min-h-screen">
        {/* Left Sidebar */}
        <div className="w-20 bg-gray-50 border-r">
          <div className="py-2">
            {categories.map((category) => (
              <div
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-2 py-4 text-center cursor-pointer transition-colors ${
                  activeCategory === category.id ? category.color : "hover:bg-gray-100"
                }`}
              >
                {category.icon && <div className="text-lg mb-1">{category.icon}</div>}
                <div className="text-xs font-medium leading-tight">{category.name}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 pb-32">
          {products.map((product) => (
            <div key={product.id} className="border-b hover:bg-gray-50 transition-colors">
              <div className="p-4 flex gap-4">
                <img
                  src={product.image || "/placeholder.svg"}
                  alt={product.name}
                  className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm mb-2 line-clamp-2 leading-relaxed">{product.name}</h3>
                  <div className="text-xs text-red-500 mb-3 bg-red-50 px-2 py-1 rounded inline-block">
                    {product.discount}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <span className="text-red-500 font-bold text-lg">￥{product.currentPrice}</span>
                        {product.originalPrice > product.currentPrice && (
                          <span className="text-gray-400 line-through text-sm">￥{product.originalPrice}</span>
                        )}
                      </div>
                      <span className="text-xs text-orange-600">{product.badge}</span>
                    </div>
                    <Button
                      size="sm"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black w-10 h-10 rounded-full p-0 flex-shrink-0"
                    >
                      <Plus className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Delivery Info */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t">
        {/* Delivery Info */}
        <div className="px-3 py-3 border-b">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">预估加打包费￥0.5 配送费￥4</div>
            <div className="flex items-center gap-3">
              <span className="text-lg font-bold text-red-500">￥20起送</span>
            </div>
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className="flex">
          <button className="flex-1 text-center py-3 hover:bg-gray-50 transition-colors">
            <MessageCircle className="w-6 h-6 mx-auto mb-1 text-gray-600" />
            <span className="text-xs text-gray-600">问商家</span>
          </button>
          <button className="flex-1 text-center py-3 hover:bg-gray-50 transition-colors">
            <ShoppingCart className="w-6 h-6 mx-auto mb-1 text-gray-600" />
            <span className="text-xs text-gray-600">购物车</span>
          </button>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}
