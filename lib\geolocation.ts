import { getDistance } from 'geolib';

// 用户位置接口
export interface UserLocation {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

// 药房位置接口
export interface PharmacyLocation {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  address: string;
}

// 距离计算结果接口
export interface DistanceResult {
  pharmacyId: string;
  distance: number; // 公里
  duration: number; // 分钟，预估
  formattedDistance: string;
}

/**
 * 获取用户当前位置
 */
export async function getCurrentPosition(): Promise<UserLocation> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        });
      },
      (error) => {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            reject(new Error('用户拒绝了位置权限请求'));
            break;
          case error.POSITION_UNAVAILABLE:
            reject(new Error('位置信息不可用'));
            break;
          case error.TIMEOUT:
            reject(new Error('获取位置超时'));
            break;
          default:
            reject(new Error('获取位置时发生未知错误'));
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5分钟缓存
      }
    );
  });
}

/**
 * 监听位置变化
 */
export function watchPosition(
  callback: (position: UserLocation) => void,
  errorCallback?: (error: Error) => void
): number {
  if (!navigator.geolocation) {
    throw new Error('Geolocation is not supported by this browser');
  }

  return navigator.geolocation.watchPosition(
    (position) => {
      callback({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      });
    },
    (error) => {
      if (errorCallback) {
        errorCallback(new Error(`位置监听错误: ${error.message}`));
      }
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000, // 1分钟缓存
    }
  );
}

/**
 * 清除位置监听
 */
export function clearWatch(watchId: number): void {
  if (navigator.geolocation) {
    navigator.geolocation.clearWatch(watchId);
  }
}

/**
 * 计算两点间距离（公里）
 */
export function calculateDistance(
  point1: { latitude: number; longitude: number },
  point2: { latitude: number; longitude: number }
): number {
  const distance = getDistance(point1, point2);
  return Math.round(distance / 1000 * 10) / 10; // 转换为公里，保留1位小数
}

/**
 * 格式化距离显示
 */
export function formatDistance(distance: number): string {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  } else if (distance < 10) {
    return `${distance.toFixed(1)}km`;
  } else {
    return `${Math.round(distance)}km`;
  }
}

/**
 * 预估配送时间（分钟）
 */
export function estimateDeliveryTime(distance: number): number {
  // 基础时间 + 距离相关时间
  const baseTime = 10; // 基础准备时间
  const timePerKm = 3; // 每公里3分钟
  return Math.round(baseTime + distance * timePerKm);
}

/**
 * 计算药房距离和配送时间
 */
export function calculatePharmacyDistance(
  pharmacyLocation: PharmacyLocation,
  userLocation: UserLocation
): DistanceResult {
  const distance = calculateDistance(userLocation, pharmacyLocation);
  const duration = estimateDeliveryTime(distance);

  return {
    pharmacyId: pharmacyLocation.id,
    distance,
    duration,
    formattedDistance: formatDistance(distance),
  };
}

/**
 * 检查地理位置是否可用
 */
export function isGeolocationAvailable(): boolean {
  return 'geolocation' in navigator;
}

/**
 * 检查位置权限状态
 */
export async function checkLocationPermission(): Promise<PermissionState> {
  if (!navigator.permissions) {
    return 'prompt'; // 无法检查权限状态
  }

  try {
    const result = await navigator.permissions.query({ name: 'geolocation' });
    return result.state;
  } catch {
    return 'prompt';
  }
}
