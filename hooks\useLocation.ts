"use client"

import { useState, useEffect, useCallback } from 'react';
import {
  getCurrentPosition,
  watchPosition,
  clearWatch,
  checkLocationPermission,
  isGeolocationAvailable,
  type UserLocation
} from '@/lib/geolocation';

export interface LocationState {
  location: UserLocation | null;
  loading: boolean;
  error: string | null;
  permission: PermissionState;
  available: boolean;
}

export interface UseLocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  autoRequest?: boolean;
  watch?: boolean;
}

export function useLocation(options: UseLocationOptions = {}) {
  const {
    enableHighAccuracy = true,
    timeout = 10000,
    maximumAge = 300000,
    autoRequest = false,
    watch = false,
  } = options;

  const [state, setState] = useState<LocationState>({
    location: null,
    loading: false,
    error: null,
    permission: 'prompt',
    available: isGeolocationAvailable(),
  });

  const [watchId, setWatchId] = useState<number | null>(null);

  // 检查权限状态
  const checkPermission = useCallback(async () => {
    try {
      const permission = await checkLocationPermission();
      setState(prev => ({ ...prev, permission }));
      return permission;
    } catch (error) {
      console.error('检查位置权限失败:', error);
      return 'prompt' as PermissionState;
    }
  }, []);

  // 获取当前位置
  const getLocation = useCallback(async () => {
    if (!state.available) {
      setState(prev => ({
        ...prev,
        error: '此浏览器不支持地理位置功能'
      }));
      return null;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const location = await getCurrentPosition();
      setState(prev => ({
        ...prev,
        location,
        loading: false,
        error: null
      }));
      return location;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取位置失败';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
      return null;
    }
  }, [state.available]);

  // 开始监听位置变化
  const startWatching = useCallback(() => {
    if (!state.available || watchId !== null) return;

    try {
      const id = watchPosition(
        (location) => {
          setState(prev => ({
            ...prev,
            location,
            error: null
          }));
        },
        (error) => {
          setState(prev => ({
            ...prev,
            error: error.message
          }));
        }
      );
      setWatchId(id);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '开始位置监听失败'
      }));
    }
  }, [state.available, watchId]);

  // 停止监听位置变化
  const stopWatching = useCallback(() => {
    if (watchId !== null) {
      clearWatch(watchId);
      setWatchId(null);
    }
  }, [watchId]);

  // 重新获取位置
  const refreshLocation = useCallback(async () => {
    return await getLocation();
  }, [getLocation]);

  // 清除错误
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // 初始化
  useEffect(() => {
    checkPermission();

    if (autoRequest) {
      getLocation();
    }

    if (watch) {
      startWatching();
    }

    // 清理函数
    return () => {
      if (watchId !== null) {
        clearWatch(watchId);
      }
    };
  }, [autoRequest, watch, checkPermission, getLocation, startWatching, watchId]);

  return {
    // 状态
    location: state.location,
    loading: state.loading,
    error: state.error,
    permission: state.permission,
    available: state.available,

    // 方法
    getLocation,
    refreshLocation,
    startWatching,
    stopWatching,
    clearError,
    checkPermission,

    // 辅助属性
    hasLocation: state.location !== null,
    canRequestLocation: state.permission === 'granted' || state.permission === 'prompt',
    isWatching: watchId !== null,
  };
}
