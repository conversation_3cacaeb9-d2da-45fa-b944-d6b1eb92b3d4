"use client"

import { ChevronLeft, <PERSON>, Star, Shield, Truck, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gray-50 max-w-sm mx-auto overflow-x-hidden">
      {/* Status Bar */}
      <div className="flex justify-between items-center px-3 py-2 bg-white text-sm">
        <div className="flex items-center gap-1">
          <span className="font-medium">21:38</span>
          <div className="flex gap-1">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">🐵</span>
              </div>
            ))}
            <span className="text-gray-400 text-xs">•••</span>
          </div>
        </div>
        <div className="flex items-center gap-1 text-gray-600 text-xs">
          <span>👁</span>
          <span>📱</span>
          <span>🔔</span>
          <span>📶</span>
          <span>5G</span>
          <span>📶</span>
          <span className="border rounded px-1">(36)</span>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-3 py-3 flex items-center gap-2 border-b">
        <ChevronLeft className="w-5 h-5 flex-shrink-0" />
        <h1 className="text-lg font-medium flex-1 text-center mr-5">会员价格</h1>
      </div>

      {/* Pricing Plans */}
      <div className="px-3 py-4 space-y-4">
        {/* Basic Plan */}
        <Card className="p-4 bg-white border-2 border-yellow-400 relative">
          <Badge className="absolute -top-2 left-4 bg-yellow-400 text-black text-xs px-2">推荐</Badge>
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-lg font-bold text-black">基础会员</h3>
              <p className="text-sm text-gray-500">适合普通用户</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-yellow-600">¥9.9</div>
              <div className="text-xs text-gray-500">每月</div>
            </div>
          </div>
          <ul className="space-y-2 mb-4">
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>免费配送（满¥20）</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>专属客服服务</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>每日特价商品提醒</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>基础健康咨询</span>
            </li>
          </ul>
          <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black">
            立即开通
          </Button>
        </Card>

        {/* Premium Plan */}
        <Card className="p-4 bg-white border-2 border-orange-500 relative">
          <Badge className="absolute -top-2 left-4 bg-orange-500 text-white text-xs px-2">热门</Badge>
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-lg font-bold text-black flex items-center gap-2">
                高级会员
                <Star className="w-5 h-5 text-orange-500" />
              </h3>
              <p className="text-sm text-gray-500">适合经常购药用户</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-orange-600">¥19.9</div>
              <div className="text-xs text-gray-500">每月</div>
            </div>
          </div>
          <ul className="space-y-2 mb-4">
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>全部基础会员权益</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>无门槛免费配送</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>优先配送服务</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>专业药师咨询</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>用药提醒服务</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>每月健康报告</span>
            </li>
          </ul>
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            立即开通
          </Button>
        </Card>

        {/* VIP Plan */}
        <Card className="p-4 bg-white border-2 border-red-500 relative">
          <Badge className="absolute -top-2 left-4 bg-red-500 text-white text-xs px-2">尊享</Badge>
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-lg font-bold text-black flex items-center gap-2">
                尊贵会员
                <Shield className="w-5 h-5 text-red-500" />
              </h3>
              <p className="text-sm text-gray-500">适合有特殊需求的用户</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-red-600">¥39.9</div>
              <div className="text-xs text-gray-500">每月</div>
            </div>
          </div>
          <ul className="space-y-2 mb-4">
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>全部高级会员权益</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>24小时紧急送药</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>专属健康顾问</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>定期体检提醒</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>家庭药箱管理</span>
            </li>
            <li className="flex items-center gap-2 text-sm">
              <Check className="w-4 h-4 text-green-500" />
              <span>专属优惠券包</span>
            </li>
          </ul>
          <Button className="w-full bg-red-500 hover:bg-red-600 text-white">
            立即开通
          </Button>
        </Card>
      </div>

      {/* Features Section */}
      <div className="px-3 py-4">
        <h2 className="text-lg font-bold text-center mb-4">为什么选择我们的会员服务？</h2>
        <div className="grid grid-cols-1 gap-4">
          <Card className="p-4 bg-white">
            <div className="flex items-center gap-3">
              <Truck className="w-8 h-8 text-blue-500" />
              <div>
                <h3 className="font-medium">极速配送</h3>
                <p className="text-sm text-gray-500">最快30分钟送达，保障用药及时</p>
              </div>
            </div>
          </Card>
          <Card className="p-4 bg-white">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-green-500" />
              <div>
                <h3 className="font-medium">正品保障</h3>
                <p className="text-sm text-gray-500">所有药品均来自正规渠道，质量有保障</p>
              </div>
            </div>
          </Card>
          <Card className="p-4 bg-white">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-orange-500" />
              <div>
                <h3 className="font-medium">24小时服务</h3>
                <p className="text-sm text-gray-500">随时随地，满足您的用药需求</p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="px-3 py-4 pb-20">
        <h2 className="text-lg font-bold text-center mb-4">常见问题</h2>
        <div className="space-y-3">
          <Card className="p-4 bg-white">
            <h3 className="font-medium text-sm mb-2">会员可以随时取消吗？</h3>
            <p className="text-xs text-gray-500">是的，您可以随时在个人中心取消会员服务，取消后将不再续费。</p>
          </Card>
          <Card className="p-4 bg-white">
            <h3 className="font-medium text-sm mb-2">配送范围是什么？</h3>
            <p className="text-xs text-gray-500">我们覆盖全市主要区域，具体配送范围以APP显示为准。</p>
          </Card>
          <Card className="p-4 bg-white">
            <h3 className="font-medium text-sm mb-2">如何联系客服？</h3>
            <p className="text-xs text-gray-500">您可以通过APP内客服功能，或拨打客服热线400-123-4567。</p>
          </Card>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t">
        <div className="flex justify-around py-2">
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <div className="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
              <span className="text-xs">🏠</span>
            </div>
            <span className="text-xs">首页</span>
          </div>
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <div className="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
              <span className="text-xs">👨</span>
            </div>
            <span className="text-xs">问医生</span>
          </div>
          <div className="flex flex-col items-center gap-1 text-yellow-500">
            <div className="w-6 h-6 bg-yellow-400 rounded-full"></div>
            <span className="text-xs">会员</span>
          </div>
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <div className="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
              <span className="text-xs">😊</span>
            </div>
            <span className="text-xs">我的</span>
          </div>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-16"></div>
    </div>
  )
}
